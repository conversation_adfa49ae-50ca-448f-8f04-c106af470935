/**
 * Excel导出功能使用示例
 * 
 * 这个文件展示了如何使用教师评价问卷系统的Excel导出功能
 */

// 前端JavaScript示例

/**
 * 导出学校统计数据
 */
async function exportSchoolStatistics() {
  const data = {
    sso_school_code: 'SCHOOL001',
    month: '2024-03',
    include_trend: true,
    include_teacher_ranking: true,
    include_incomplete_students: true
  };

  try {
    const response = await fetch('/api/export/school-statistics', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    });

    if (response.ok) {
      const blob = await response.blob();
      downloadFile(blob, '学校统计报表.xlsx');
    } else {
      const error = await response.json();
      console.error('导出失败:', error.msg);
    }
  } catch (error) {
    console.error('导出失败:', error);
  }
}

/**
 * 导出教师统计数据
 */
async function exportTeacherStatistics() {
  const data = {
    sso_school_code: 'SCHOOL001',
    sso_teacher_id: 'TEACHER001',
    month: '2024-03',
    include_distribution: true,
    include_keywords: true,
    include_trend: true
  };

  try {
    const response = await fetch('/api/export/teacher-statistics', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    });

    if (response.ok) {
      const blob = await response.blob();
      downloadFile(blob, '教师统计报表.xlsx');
    } else {
      const error = await response.json();
      console.error('导出失败:', error.msg);
    }
  } catch (error) {
    console.error('导出失败:', error);
  }
}

/**
 * 导出教师排名数据
 */
async function exportTeacherRanking() {
  const data = {
    sso_school_code: 'SCHOOL001',
    month: '2024-03',
    subject: '数学',
    sort_by: 'average_score',
    sort_order: 'DESC',
    limit: 50
  };

  try {
    const response = await fetch('/api/export/teacher-ranking', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    });

    if (response.ok) {
      const blob = await response.blob();
      downloadFile(blob, '教师排名报表.xlsx');
    } else {
      const error = await response.json();
      console.error('导出失败:', error.msg);
    }
  } catch (error) {
    console.error('导出失败:', error);
  }
}

/**
 * 导出问卷响应数据
 */
async function exportQuestionnaireResponses() {
  const data = {
    sso_school_code: 'SCHOOL001',
    questionnaire_id: 1,
    month: '2024-03',
    include_answers: true
  };

  try {
    const response = await fetch('/api/export/questionnaire-responses', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    });

    if (response.ok) {
      const blob = await response.blob();
      downloadFile(blob, '问卷响应数据.xlsx');
    } else {
      const error = await response.json();
      console.error('导出失败:', error.msg);
    }
  } catch (error) {
    console.error('导出失败:', error);
  }
}

/**
 * 导出综合报表
 */
async function exportComprehensiveReport() {
  const data = {
    sso_school_code: 'SCHOOL001',
    start_month: '2024-01',
    end_month: '2024-03',
    include_school_summary: true,
    include_teacher_ranking: true,
    include_trend_analysis: true,
    include_completion_analysis: true
  };

  try {
    const response = await fetch('/api/export/comprehensive-report', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    });

    if (response.ok) {
      const blob = await response.blob();
      downloadFile(blob, '综合报表.xlsx');
    } else {
      const error = await response.json();
      console.error('导出失败:', error.msg);
    }
  } catch (error) {
    console.error('导出失败:', error);
  }
}

/**
 * 导出未完成学生名单
 */
async function exportIncompleteStudents() {
  const data = {
    sso_school_code: 'SCHOOL001',
    questionnaire_id: 1,
    month: '2024-03',
    include_contact_info: true
  };

  try {
    const response = await fetch('/api/export/incomplete-students', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    });

    if (response.ok) {
      const blob = await response.blob();
      downloadFile(blob, '未完成学生名单.xlsx');
    } else {
      const error = await response.json();
      console.error('导出失败:', error.msg);
    }
  } catch (error) {
    console.error('导出失败:', error);
  }
}

/**
 * 通用导出函数
 */
async function exportGeneral(exportType, params) {
  const data = {
    export_type: exportType,
    ...params
  };

  try {
    const response = await fetch('/api/export/general', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    });

    if (response.ok) {
      const blob = await response.blob();
      const filename = getFilenameFromResponse(response) || `${exportType}_报表.xlsx`;
      downloadFile(blob, filename);
    } else {
      const error = await response.json();
      console.error('导出失败:', error.msg);
    }
  } catch (error) {
    console.error('导出失败:', error);
  }
}

/**
 * 下载文件
 */
function downloadFile(blob, filename) {
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  window.URL.revokeObjectURL(url);
  document.body.removeChild(a);
}

/**
 * 从响应头中获取文件名
 */
function getFilenameFromResponse(response) {
  const contentDisposition = response.headers.get('Content-Disposition');
  if (contentDisposition) {
    const matches = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
    if (matches && matches[1]) {
      return decodeURIComponent(matches[1].replace(/['"]/g, ''));
    }
  }
  return null;
}

/**
 * 批量导出示例
 */
async function batchExport() {
  const schoolCode = 'SCHOOL001';
  const month = '2024-03';

  console.log('开始批量导出...');

  // 导出学校统计
  await exportGeneral('school_statistics', {
    sso_school_code: schoolCode,
    month: month,
    include_trend: true,
    include_teacher_ranking: true
  });

  // 等待一秒避免并发过多
  await new Promise(resolve => setTimeout(resolve, 1000));

  // 导出教师排名
  await exportGeneral('teacher_ranking', {
    sso_school_code: schoolCode,
    month: month,
    sort_by: 'average_score',
    sort_order: 'DESC',
    limit: 100
  });

  // 等待一秒
  await new Promise(resolve => setTimeout(resolve, 1000));

  // 导出问卷响应
  await exportGeneral('questionnaire_responses', {
    sso_school_code: schoolCode,
    month: month,
    include_answers: true
  });

  console.log('批量导出完成');
}

/**
 * 带进度条的导出示例
 */
async function exportWithProgress(exportType, params, progressCallback) {
  try {
    progressCallback && progressCallback(0, '开始导出...');

    const response = await fetch('/api/export/general', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        export_type: exportType,
        ...params
      })
    });

    progressCallback && progressCallback(50, '正在生成文件...');

    if (response.ok) {
      const blob = await response.blob();
      progressCallback && progressCallback(90, '正在下载文件...');
      
      const filename = getFilenameFromResponse(response) || `${exportType}_报表.xlsx`;
      downloadFile(blob, filename);
      
      progressCallback && progressCallback(100, '导出完成');
    } else {
      const error = await response.json();
      throw new Error(error.msg || '导出失败');
    }
  } catch (error) {
    progressCallback && progressCallback(-1, `导出失败: ${error.message}`);
    throw error;
  }
}

// 使用示例
if (typeof window !== 'undefined') {
  // 在浏览器环境中，可以将这些函数绑定到按钮点击事件
  window.exportFunctions = {
    exportSchoolStatistics,
    exportTeacherStatistics,
    exportTeacherRanking,
    exportQuestionnaireResponses,
    exportComprehensiveReport,
    exportIncompleteStudents,
    exportGeneral,
    batchExport,
    exportWithProgress
  };
}

// Node.js环境示例
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    exportSchoolStatistics,
    exportTeacherStatistics,
    exportTeacherRanking,
    exportQuestionnaireResponses,
    exportComprehensiveReport,
    exportIncompleteStudents,
    exportGeneral,
    batchExport,
    exportWithProgress
  };
}
