import { Inject, Provide } from '@midwayjs/core';
import { InjectRepository, InjectDataSource } from '@midwayjs/sequelize';
import { Repository } from 'sequelize-typescript';
import { Sequelize, QueryTypes } from 'sequelize';
import { CustomError } from '../error/custom.error';
import { Response } from '../entity/response.entity';
import { Answer } from '../entity/answer.entity';
import { Questionnaire } from '../entity/questionnaire.entity';
import {
  SchoolStatisticsQueryDTO,
  TeacherStatisticsQueryDTO,
  TeacherRankingQueryDTO,
  TrendAnalysisQueryDTO,
} from '../dto/statistics.dto';

import {
  ISchoolStatistics,
  ITeacherStatistics,
  ITeacherRankingResponse,
  IIncompleteStudentsSummary,
  IIncompleteStudent,
  IIncompleteStudentsByClass,
  IIncompleteStudentsResponse,
} from '../interface';
import { Custome } from './api_sso/custome.service';

@Provide()
export class StatisticsService {
  @InjectRepository(Response)
  responseRepository: Repository<Response>;

  @InjectRepository(Answer)
  answerRepository: Repository<Answer>;

  @InjectRepository(Questionnaire)
  questionnaireRepository: Repository<Questionnaire>;

  @InjectDataSource()
  sequelize: Sequelize;

  @Inject()
  customeService: Custome;

  /**
   * 获取学校维度统计
   * @param queryDto 查询条件
   * @returns 学校统计数据
   */
  async getSchoolStatistics(
    queryDto: SchoolStatisticsQueryDTO
  ): Promise<ISchoolStatistics> {
    const {
      sso_school_code,
      month,
      start_month,
      end_month,
      include_trend,
      include_teacher_ranking,
    } = queryDto;

    // 构建基础查询条件
    let whereClause = 'q.sso_school_code = :sso_school_code';
    const replacements: any = { sso_school_code };

    if (month) {
      whereClause += ' AND r.month = :month';
      replacements.month = month;
    } else if (start_month && end_month) {
      whereClause += ' AND r.month BETWEEN :start_month AND :end_month';
      replacements.start_month = start_month;
      replacements.end_month = end_month;
    }

    // 基础统计查询
    const basicStatsQuery = `
      SELECT
        q.sso_school_code,
        q.sso_school_name,
        COUNT(DISTINCT r.id) as total_responses,
        COUNT(DISTINCT CASE WHEN r.is_completed = 1 THEN r.id END) as completed_responses,
        ROUND(
          COUNT(DISTINCT CASE WHEN r.is_completed = 1 THEN r.id END) * 100.0 /
          NULLIF(COUNT(DISTINCT r.id), 0), 2
        ) as completion_rate,
        ROUND(AVG(r.school_rating), 2) as school_average_score,
        ROUND(AVG(r.total_average_score), 2) as teacher_average_score,
        COUNT(DISTINCT a.sso_teacher_id) as total_teachers_evaluated
      FROM questionnaires q
      LEFT JOIN responses r ON q.id = r.questionnaire_id
      LEFT JOIN answers a ON r.id = a.response_id
      WHERE ${whereClause}
      GROUP BY q.sso_school_code, q.sso_school_name
    `;

    const [basicStats] = (await this.sequelize.query(basicStatsQuery, {
      type: QueryTypes.SELECT,
      replacements,
    })) as any[];

    if (!basicStats) {
      throw new CustomError('未找到该学校的统计数据');
    }

    // 获取学校所有学生数据
    const allStudents = await this.customeService.getStudents({
      enterpriseCode: sso_school_code,
    });
    const totalStudents = allStudents.length;

    // 重新计算基于学生总数的完成率
    const completedResponses = parseInt(basicStats.completed_responses) || 0;
    const accurateCompletionRate =
      totalStudents > 0
        ? Math.round((completedResponses / totalStudents) * 100 * 100) / 100
        : 0;

    const result: ISchoolStatistics = {
      sso_school_code: basicStats.sso_school_code,
      sso_school_name: basicStats.sso_school_name,
      month: month,
      total_responses: parseInt(basicStats.total_responses) || 0,
      completed_responses: completedResponses,
      total_students: totalStudents,
      completion_rate: accurateCompletionRate,
      school_average_score: parseFloat(basicStats.school_average_score) || 0,
      teacher_average_score: parseFloat(basicStats.teacher_average_score) || 0,
      total_teachers_evaluated:
        parseInt(basicStats.total_teachers_evaluated) || 0,
    };

    // 如果需要趋势数据
    if (include_trend && start_month && end_month) {
      result.response_trend = await this.getResponseTrend(
        sso_school_code,
        start_month,
        end_month
      );
    }

    // 如果需要教师排名
    if (include_teacher_ranking) {
      const rankingResult = await this.getTeacherRanking({
        sso_school_code,
        month,
        page: 1,
        limit: 10,
        sort_by: 'average_score',
        sort_order: 'DESC',
      });
      result.teacher_ranking = rankingResult.list;
    }

    // 获取未填写学生统计（如果有指定月份）
    if (month) {
      result.incomplete_students_summary =
        await this.getIncompleteStudentsSummary(
          sso_school_code,
          undefined,
          month
        );
    }

    return result;
  }

  /**
   * 获取教师维度统计
   * @param queryDto 查询条件
   * @returns 教师统计数据
   */
  async getTeacherStatistics(
    queryDto: TeacherStatisticsQueryDTO
  ): Promise<ITeacherStatistics> {
    const {
      sso_teacher_id,
      sso_school_code,
      month,
      start_month,
      end_month,
      include_distribution,
      include_keywords,
      include_trend,
    } = queryDto;

    // 构建查询条件
    let whereClause = 'a.sso_teacher_id = :sso_teacher_id';
    const replacements: any = { sso_teacher_id };

    if (sso_school_code) {
      whereClause += ' AND q.sso_school_code = :sso_school_code';
      replacements.sso_school_code = sso_school_code;
    }

    if (month) {
      whereClause += ' AND r.month = :month';
      replacements.month = month;
    } else if (start_month && end_month) {
      whereClause += ' AND r.month BETWEEN :start_month AND :end_month';
      replacements.start_month = start_month;
      replacements.end_month = end_month;
    }

    // 基础统计查询
    const basicStatsQuery = `
      SELECT
        a.sso_teacher_id,
        a.sso_teacher_name,
        a.sso_teacher_subject,
        a.sso_teacher_department,
        COUNT(a.id) as total_evaluations,
        ROUND(AVG(a.rating), 2) as average_score
      FROM answers a
      JOIN responses r ON a.response_id = r.id
      JOIN questionnaires q ON r.questionnaire_id = q.id
      WHERE ${whereClause}
      GROUP BY a.sso_teacher_id, a.sso_teacher_name, a.sso_teacher_subject, a.sso_teacher_department
    `;

    const [basicStats] = (await this.sequelize.query(basicStatsQuery, {
      type: QueryTypes.SELECT,
      replacements,
    })) as any[];

    if (!basicStats) {
      throw new CustomError('未找到该教师的统计数据');
    }

    const averageScore = parseFloat(basicStats.average_score) || 0;
    const result: ITeacherStatistics = {
      sso_teacher_id: basicStats.sso_teacher_id,
      sso_teacher_name: basicStats.sso_teacher_name,
      sso_teacher_subject: basicStats.sso_teacher_subject,
      sso_teacher_department: basicStats.sso_teacher_department,
      month: month,
      total_evaluations: parseInt(basicStats.total_evaluations) || 0,
      average_score: averageScore,
      recommendation_rate: this.calculateRecommendationRate(averageScore),
      detailed_scores: {
        teaching_quality: averageScore,
        teaching_attitude: averageScore,
        classroom_management: averageScore,
        communication: averageScore,
        professional_knowledge: averageScore,
      },
    };

    // 如果需要评分分布
    if (include_distribution) {
      result.score_distribution = await this.getScoreDistribution(
        sso_teacher_id,
        sso_school_code,
        month
      );
    }

    // 如果需要关键词云
    if (include_keywords) {
      result.keyword_cloud = await this.getKeywordCloud(
        sso_teacher_id,
        sso_school_code,
        month
      );
    }

    // 如果需要趋势数据
    if (include_trend && start_month && end_month) {
      result.evaluation_trend = await this.getTeacherTrend(
        sso_teacher_id,
        sso_school_code,
        start_month,
        end_month
      );
    }

    return result;
  }

  /**
   * 获取教师排名
   * @param queryDto 查询条件
   * @returns 教师排名列表
   */
  async getTeacherRanking(
    queryDto: TeacherRankingQueryDTO
  ): Promise<ITeacherRankingResponse> {
    const {
      sso_school_code,
      month,
      subject,
      department,
      page = 1,
      limit = 20,
      sort_by = 'average_score',
      sort_order = 'DESC',
    } = queryDto;

    // 构建查询条件
    let whereClause = 'q.sso_school_code = :sso_school_code';
    const replacements: any = { sso_school_code };

    if (month) {
      whereClause += ' AND r.month = :month';
      replacements.month = month;
    }

    if (subject) {
      whereClause += ' AND a.sso_teacher_subject = :subject';
      replacements.subject = subject;
    }

    if (department) {
      whereClause += ' AND a.sso_teacher_department = :department';
      replacements.department = department;
    }

    // 排序字段映射
    const sortFieldMap = {
      average_score: 'average_score',
      evaluation_count: 'evaluation_count',
      recommendation_rate: 'recommendation_rate',
    };

    const sortField = sortFieldMap[sort_by] || 'average_score';

    const rankingQuery = `
      SELECT
        a.sso_teacher_id,
        a.sso_teacher_name,
        a.sso_teacher_subject,
        a.sso_teacher_department,
        COUNT(a.id) as evaluation_count,
        ROUND(AVG(a.rating), 2) as average_score
      FROM answers a
      JOIN responses r ON a.response_id = r.id
      JOIN questionnaires q ON r.questionnaire_id = q.id
      WHERE ${whereClause}
      GROUP BY a.sso_teacher_id, a.sso_teacher_name, a.sso_teacher_subject, a.sso_teacher_department
      ORDER BY ${sortField} ${sort_order}
      LIMIT :limit OFFSET :offset
    `;

    const offset = (page - 1) * limit;
    replacements.limit = limit;
    replacements.offset = offset;

    const rankings = (await this.sequelize.query(rankingQuery, {
      type: QueryTypes.SELECT,
      replacements,
    })) as any[];

    // 获取总数
    const countQuery = `
      SELECT COUNT(DISTINCT a.sso_teacher_id) as total
      FROM answers a
      JOIN responses r ON a.response_id = r.id
      JOIN questionnaires q ON r.questionnaire_id = q.id
      WHERE ${whereClause}
    `;

    const [countResult] = (await this.sequelize.query(countQuery, {
      type: QueryTypes.SELECT,
      replacements: { sso_school_code, month, subject, department },
    })) as any[];

    const list = rankings.map((item, index) => {
      const averageScore = parseFloat(item.average_score) || 0;
      return {
        sso_teacher_id: item.sso_teacher_id,
        sso_teacher_name: item.sso_teacher_name,
        sso_teacher_subject: item.sso_teacher_subject,
        sso_teacher_department: item.sso_teacher_department,
        average_score: averageScore,
        evaluation_count: parseInt(item.evaluation_count) || 0,
        recommendation_rate: this.calculateRecommendationRate(averageScore),
        rank: offset + index + 1,
      };
    });

    return {
      list,
      total: parseInt(countResult.total) || 0,
    };
  }

  /**
   * 获取响应趋势数据
   * @param ssoSchoolId 学校ID
   * @param startMonth 开始月份
   * @param endMonth 结束月份
   * @returns 趋势数据
   */
  async getResponseTrend(
    ssoSchoolId: string,
    startMonth: string,
    endMonth: string
  ): Promise<any[]> {
    const trendQuery = `
      SELECT
        r.month,
        COUNT(DISTINCT r.id) as total_responses,
        COUNT(DISTINCT CASE WHEN r.is_completed = 1 THEN r.id END) as completed_responses,
        ROUND(AVG(r.school_rating), 2) as avg_school_score,
        ROUND(AVG(r.total_average_score), 2) as avg_teacher_score
      FROM responses r
      JOIN questionnaires q ON r.questionnaire_id = q.id
      WHERE q.sso_school_code = :sso_school_code
        AND r.month BETWEEN :start_month AND :end_month
      GROUP BY r.month
      ORDER BY r.month
    `;

    const trendData = (await this.sequelize.query(trendQuery, {
      type: QueryTypes.SELECT,
      replacements: {
        sso_school_code: ssoSchoolId,
        start_month: startMonth,
        end_month: endMonth,
      },
    })) as any[];

    return trendData.map(item => ({
      month: item.month,
      total_responses: parseInt(item.total_responses) || 0,
      completed_responses: parseInt(item.completed_responses) || 0,
      completion_rate:
        item.total_responses > 0
          ? Math.round(
              (item.completed_responses / item.total_responses) * 100 * 100
            ) / 100
          : 0,
      avg_school_score: parseFloat(item.avg_school_score) || 0,
      avg_teacher_score: parseFloat(item.avg_teacher_score) || 0,
    }));
  }

  /**
   * 获取教师趋势数据
   * @param ssoTeacherId 教师ID
   * @param ssoSchoolId 学校ID
   * @param startMonth 开始月份
   * @param endMonth 结束月份
   * @returns 教师趋势数据
   */
  async getTeacherTrend(
    ssoTeacherId: string,
    ssoSchoolId?: string,
    startMonth?: string,
    endMonth?: string
  ): Promise<any[]> {
    let whereClause = 'a.sso_teacher_id = :sso_teacher_id';
    const replacements: any = { sso_teacher_id: ssoTeacherId };

    if (ssoSchoolId) {
      whereClause += ' AND q.sso_school_code = :sso_school_code';
      replacements.sso_school_code = ssoSchoolId;
    }

    if (startMonth && endMonth) {
      whereClause += ' AND r.month BETWEEN :start_month AND :end_month';
      replacements.start_month = startMonth;
      replacements.end_month = endMonth;
    }

    const trendQuery = `
      SELECT
        r.month,
        COUNT(a.id) as evaluation_count,
        ROUND(AVG(a.rating), 2) as average_score
      FROM answers a
      JOIN responses r ON a.response_id = r.id
      JOIN questionnaires q ON r.questionnaire_id = q.id
      WHERE ${whereClause}
      GROUP BY r.month
      ORDER BY r.month
    `;

    const trendData = (await this.sequelize.query(trendQuery, {
      type: QueryTypes.SELECT,
      replacements,
    })) as any[];

    return trendData.map(item => ({
      month: item.month,
      evaluation_count: parseInt(item.evaluation_count) || 0,
      average_score: parseFloat(item.average_score) || 0,
    }));
  }

  /**
   * 获取教师评分分布
   * @param ssoTeacherId 教师ID
   * @param ssoSchoolId 学校ID
   * @param month 月份
   * @returns 评分分布
   */
  async getScoreDistribution(
    ssoTeacherId: string,
    ssoSchoolId?: string,
    month?: string
  ): Promise<any> {
    let whereClause = 'a.sso_teacher_id = :sso_teacher_id';
    const replacements: any = { sso_teacher_id: ssoTeacherId };

    if (ssoSchoolId) {
      whereClause += ' AND q.sso_school_code = :sso_school_code';
      replacements.sso_school_code = ssoSchoolId;
    }

    if (month) {
      whereClause += ' AND r.month = :month';
      replacements.month = month;
    }

    // 先获取总数
    const totalCountQuery = `
      SELECT COUNT(*) as total_count
      FROM answers a
      JOIN responses r ON a.response_id = r.id
      JOIN questionnaires q ON r.questionnaire_id = q.id
      WHERE ${whereClause}
    `;

    const [totalResult] = (await this.sequelize.query(totalCountQuery, {
      type: QueryTypes.SELECT,
      replacements,
    })) as any[];

    const totalCount = parseInt(totalResult?.total_count) || 0;

    if (totalCount === 0) {
      return [];
    }

    const distributionQuery = `
      SELECT
        CASE
          WHEN a.rating >= 90 THEN '90-100'
          WHEN a.rating >= 80 THEN '80-89'
          WHEN a.rating >= 70 THEN '70-79'
          WHEN a.rating >= 60 THEN '60-69'
          ELSE '60以下'
        END as score_range,
        COUNT(*) as count
      FROM answers a
      JOIN responses r ON a.response_id = r.id
      JOIN questionnaires q ON r.questionnaire_id = q.id
      WHERE ${whereClause}
      GROUP BY score_range
      ORDER BY score_range DESC
    `;

    const distribution = (await this.sequelize.query(distributionQuery, {
      type: QueryTypes.SELECT,
      replacements,
    })) as any[];

    return distribution.map(item => ({
      score_range: item.score_range,
      count: parseInt(item.count) || 0,
      percentage:
        Math.round((parseInt(item.count) / totalCount) * 100 * 100) / 100,
    }));
  }

  /**
   * 获取关键词云数据
   * @param ssoTeacherId 教师ID
   * @param ssoSchoolId 学校ID
   * @param month 月份
   * @returns 关键词云数据
   */
  async getKeywordCloud(
    ssoTeacherId: string,
    ssoSchoolId?: string,
    month?: string
  ): Promise<any[]> {
    let whereClause = 'a.sso_teacher_id = :sso_teacher_id';
    const replacements: any = { sso_teacher_id: ssoTeacherId };

    if (ssoSchoolId) {
      whereClause += ' AND q.sso_school_code = :sso_school_code';
      replacements.sso_school_code = ssoSchoolId;
    }

    if (month) {
      whereClause += ' AND r.month = :month';
      replacements.month = month;
    }

    // 获取所有评价描述
    const keywordQuery = `
      SELECT
        a.description
      FROM answers a
      JOIN responses r ON a.response_id = r.id
      JOIN questionnaires q ON r.questionnaire_id = q.id
      WHERE ${whereClause}
        AND a.description IS NOT NULL
    `;

    const keywordData = (await this.sequelize.query(keywordQuery, {
      type: QueryTypes.SELECT,
      replacements,
    })) as any[];

    // 简单的关键词提取和统计
    const keywordCount = new Map<string, number>();

    keywordData.forEach(item => {
      // 处理文本描述（简单的关键词提取）
      if (item.description && typeof item.description === 'string') {
        // 简单的中文关键词提取（这里可以集成更复杂的NLP库）
        const keywords = this.extractKeywords(item.description);
        keywords.forEach(keyword => {
          keywordCount.set(keyword, (keywordCount.get(keyword) || 0) + 1);
        });
      }
    });

    // 转换为数组并按频次排序
    const keywordArray = Array.from(keywordCount.entries())
      .map(([word, count]) => ({ word, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 50); // 取前50个关键词

    // 计算权重（相对于最高频次的比例）
    const maxCount = keywordArray.length > 0 ? keywordArray[0].count : 1;
    return keywordArray.map(item => ({
      word: item.word,
      count: item.count,
      weight: Math.round((item.count / maxCount) * 100) / 100,
    }));
  }

  /**
   * 计算推荐率
   * @param averageScore 平均分
   * @param threshold 推荐阈值，默认80分
   * @returns 推荐率（百分比）
   */
  private calculateRecommendationRate(
    averageScore: number,
    threshold = 80
  ): number {
    if (averageScore >= threshold) {
      return 100;
    }
    return Math.round((averageScore / threshold) * 100 * 100) / 100;
  }

  /**
   * 简单的关键词提取方法
   * @param text 文本
   * @returns 关键词数组
   */
  private extractKeywords(text: string): string[] {
    // 这是一个简化的关键词提取方法
    // 在实际项目中，建议使用专业的中文分词和关键词提取库

    // 常见的正面评价词汇
    const positiveKeywords = [
      '认真',
      '负责',
      '专业',
      '耐心',
      '细心',
      '优秀',
      '棒',
      '好',
      '满意',
      '喜欢',
      '推荐',
      '赞',
      '不错',
      '很好',
      '非常好',
      '教学',
      '方法',
      '互动',
      '生动',
      '有趣',
      '清楚',
      '明白',
      '理解',
      '进步',
      '提高',
    ];

    // 常见的负面评价词汇
    const negativeKeywords = [
      '不好',
      '差',
      '不满意',
      '不喜欢',
      '问题',
      '困难',
      '不清楚',
      '不明白',
      '改进',
      '提升',
      '加强',
      '注意',
      '希望',
      '建议',
      '需要',
      '应该',
    ];

    // 教学相关词汇
    const teachingKeywords = [
      '教学',
      '课堂',
      '作业',
      '考试',
      '学习',
      '知识',
      '技能',
      '能力',
      '方法',
      '技巧',
      '经验',
      '指导',
      '帮助',
      '支持',
      '鼓励',
      '激励',
    ];

    const allKeywords = [
      ...positiveKeywords,
      ...negativeKeywords,
      ...teachingKeywords,
    ];
    const foundKeywords: string[] = [];

    allKeywords.forEach(keyword => {
      if (text.includes(keyword)) {
        foundKeywords.push(keyword);
      }
    });

    return foundKeywords;
  }

  /**
   * 获取趋势分析数据
   * @param queryDto 查询条件
   * @returns 趋势分析数据
   */
  async getTrendAnalysis(queryDto: TrendAnalysisQueryDTO): Promise<any> {
    const {
      sso_school_code,
      start_month,
      end_month,
      sso_teacher_id,
      analysis_type,
    } = queryDto;

    if (analysis_type === 'teacher' && sso_teacher_id) {
      return await this.getTeacherTrend(
        sso_teacher_id,
        sso_school_code,
        start_month,
        end_month
      );
    } else {
      return await this.getResponseTrend(
        sso_school_code,
        start_month,
        end_month
      );
    }
  }

  /**
   * 获取未填写学生名单统计（分页版本）
   * @param ssoSchoolCode 学校编码
   * @param questionnaireId 问卷ID，可选
   * @param month 月份，可选
   * @param page 页码，默认1
   * @param pageSize 每页数量，默认20
   * @param gradeCode 年级编码筛选，可选
   * @param classCode 班级编码筛选，可选
   * @returns 分页的未填写学生统计信息
   */
  async getIncompleteStudentsWithPagination(
    ssoSchoolCode: string,
    questionnaireId?: number,
    month?: string,
    page = 1,
    pageSize = 20,
    gradeCode?: string,
    classCode?: string
  ): Promise<IIncompleteStudentsResponse> {
    // 获取完整的统计数据
    const summary = await this.getIncompleteStudentsSummary(
      ssoSchoolCode,
      questionnaireId,
      month
    );

    // 应用筛选条件
    let filteredClasses = summary.by_class;

    if (gradeCode) {
      filteredClasses = filteredClasses.filter(
        cls => cls.grade_code === gradeCode
      );
    }

    if (classCode) {
      filteredClasses = filteredClasses.filter(
        cls => cls.class_code === classCode
      );
    }

    // 计算分页
    const total = filteredClasses.length;
    const totalPages = Math.ceil(total / pageSize);
    const offset = (page - 1) * pageSize;
    const paginatedClasses = filteredClasses.slice(offset, offset + pageSize);

    return {
      summary,
      pagination: {
        page,
        pageSize,
        total,
        totalPages,
      },
      classes: paginatedClasses,
    };
  }

  /**
   * 获取未填写学生名单统计
   * @param ssoSchoolCode 学校编码
   * @param questionnaireId 问卷ID，可选
   * @param month 月份，可选
   * @returns 未填写学生统计信息
   */
  async getIncompleteStudentsSummary(
    ssoSchoolCode: string,
    questionnaireId?: number,
    month?: string
  ): Promise<IIncompleteStudentsSummary> {
    // 获取学校所有学生
    const allStudents = await this.customeService.getStudents({
      enterpriseCode: ssoSchoolCode,
    });

    // 构建查询条件获取已填写问卷的学生
    let whereClause = 'q.sso_school_code = :sso_school_code';
    const replacements: any = { sso_school_code: ssoSchoolCode };

    if (questionnaireId) {
      whereClause += ' AND r.questionnaire_id = :questionnaire_id';
      replacements.questionnaire_id = questionnaireId;
    }

    if (month) {
      whereClause += ' AND r.month = :month';
      replacements.month = month;
    }

    // 查询已完成问卷的学生编码
    const completedStudentsQuery = `
      SELECT DISTINCT r.sso_student_code
      FROM responses r
      JOIN questionnaires q ON r.questionnaire_id = q.id
      WHERE ${whereClause} AND r.is_completed = 1
    `;

    const completedStudents = (await this.sequelize.query(
      completedStudentsQuery,
      {
        type: QueryTypes.SELECT,
        replacements,
      }
    )) as any[];

    const completedStudentCodes = new Set(
      completedStudents.map(item => item.sso_student_code)
    );

    // 找出未填写的学生
    const incompleteStudents: IIncompleteStudent[] = allStudents
      .filter(student => !completedStudentCodes.has(student.code))
      .map(student => ({
        sso_student_code: student.code,
        sso_student_name: student.name,
        grade_code: student.classes[0]?.grade_code || '',
        grade_name: student.classes[0]?.grade_name || '',
        class_code: student.classes[0]?.code || '',
        class_name: student.classes[0]?.name || '',
      }));

    // 按年级分组统计
    const gradeStats = new Map<
      string,
      {
        grade_code: string;
        grade_name: string;
        incomplete_count: number;
        total_students: number;
      }
    >();

    // 按班级分组统计
    const classStats = new Map<string, IIncompleteStudentsByClass>();

    // 统计所有学生（按年级和班级）
    allStudents.forEach(student => {
      const gradeCode = student.classes[0]?.grade_code || '';
      const gradeName = student.classes[0]?.grade_name || '';
      const classCode = student.classes[0]?.code || '';
      const className = student.classes[0]?.name || '';

      // 年级统计
      if (!gradeStats.has(gradeCode)) {
        gradeStats.set(gradeCode, {
          grade_code: gradeCode,
          grade_name: gradeName,
          incomplete_count: 0,
          total_students: 0,
        });
      }
      gradeStats.get(gradeCode)!.total_students++;

      // 班级统计
      const classKey = `${gradeCode}-${classCode}`;
      if (!classStats.has(classKey)) {
        classStats.set(classKey, {
          grade_code: gradeCode,
          grade_name: gradeName,
          class_code: classCode,
          class_name: className,
          incomplete_count: 0,
          total_students: 0,
          completion_rate: 0,
          students: [],
        });
      }
      classStats.get(classKey)!.total_students++;
    });

    // 统计未填写学生
    incompleteStudents.forEach(student => {
      // 年级统计
      if (gradeStats.has(student.grade_code)) {
        gradeStats.get(student.grade_code)!.incomplete_count++;
      }

      // 班级统计
      const classKey = `${student.grade_code}-${student.class_code}`;
      if (classStats.has(classKey)) {
        const classStat = classStats.get(classKey)!;
        classStat.incomplete_count++;
        classStat.students.push(student);
      }
    });

    // 计算完成率并转换为数组
    const byGrade = Array.from(gradeStats.values()).map(grade => ({
      ...grade,
      completion_rate:
        grade.total_students > 0
          ? Math.round(
              ((grade.total_students - grade.incomplete_count) /
                grade.total_students) *
                100 *
                100
            ) / 100
          : 0,
    }));

    const byClass = Array.from(classStats.values()).map(classItem => ({
      ...classItem,
      completion_rate:
        classItem.total_students > 0
          ? Math.round(
              ((classItem.total_students - classItem.incomplete_count) /
                classItem.total_students) *
                100 *
                100
            ) / 100
          : 0,
    }));

    return {
      total_incomplete: incompleteStudents.length,
      by_grade: byGrade,
      by_class: byClass,
    };
  }
}
