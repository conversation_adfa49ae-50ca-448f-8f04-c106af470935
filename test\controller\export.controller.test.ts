import { createApp, close, createHttpRequest } from '@midwayjs/mock';
import { Framework } from '@midwayjs/koa';

describe('test/controller/export.controller.test.ts', () => {
  let app;

  beforeAll(async () => {
    try {
      app = await createApp<Framework>();
    } catch (err) {
      console.error('setup error', err);
      throw err;
    }
  });

  afterAll(async () => {
    await close(app);
  });

  it('should POST /api/export/school-statistics', async () => {
    const result = await createHttpRequest(app)
      .post('/api/export/school-statistics')
      .send({
        sso_school_code: 'TEST_SCHOOL',
        month: '2024-03',
        include_trend: false,
        include_teacher_ranking: false,
        include_incomplete_students: false,
      });

    // 由于没有真实数据，期望返回错误或者Excel文件
    expect(result.status).toBeGreaterThanOrEqual(200);
    
    if (result.status === 200) {
      // 如果成功，应该返回Excel文件
      expect(result.headers['content-type']).toContain('spreadsheet');
      expect(result.headers['content-disposition']).toContain('attachment');
    } else {
      // 如果失败，应该返回错误信息
      expect(result.body).toHaveProperty('errCode');
      expect(result.body).toHaveProperty('msg');
    }
  });

  it('should POST /api/export/teacher-statistics', async () => {
    const result = await createHttpRequest(app)
      .post('/api/export/teacher-statistics')
      .send({
        sso_school_code: 'TEST_SCHOOL',
        sso_teacher_id: 'TEST_TEACHER',
        month: '2024-03',
        include_distribution: false,
        include_keywords: false,
        include_trend: false,
      });

    expect(result.status).toBeGreaterThanOrEqual(200);
    
    if (result.status === 200) {
      expect(result.headers['content-type']).toContain('spreadsheet');
      expect(result.headers['content-disposition']).toContain('attachment');
    } else {
      expect(result.body).toHaveProperty('errCode');
      expect(result.body).toHaveProperty('msg');
    }
  });

  it('should POST /api/export/teacher-ranking', async () => {
    const result = await createHttpRequest(app)
      .post('/api/export/teacher-ranking')
      .send({
        sso_school_code: 'TEST_SCHOOL',
        month: '2024-03',
        sort_by: 'average_score',
        sort_order: 'DESC',
        limit: 10,
      });

    expect(result.status).toBeGreaterThanOrEqual(200);
    
    if (result.status === 200) {
      expect(result.headers['content-type']).toContain('spreadsheet');
      expect(result.headers['content-disposition']).toContain('attachment');
    } else {
      expect(result.body).toHaveProperty('errCode');
      expect(result.body).toHaveProperty('msg');
    }
  });

  it('should POST /api/export/questionnaire-responses', async () => {
    const result = await createHttpRequest(app)
      .post('/api/export/questionnaire-responses')
      .send({
        sso_school_code: 'TEST_SCHOOL',
        month: '2024-03',
        include_answers: true,
      });

    expect(result.status).toBeGreaterThanOrEqual(200);
    
    if (result.status === 200) {
      expect(result.headers['content-type']).toContain('spreadsheet');
      expect(result.headers['content-disposition']).toContain('attachment');
    } else {
      expect(result.body).toHaveProperty('errCode');
      expect(result.body).toHaveProperty('msg');
    }
  });

  it('should POST /api/export/comprehensive-report', async () => {
    const result = await createHttpRequest(app)
      .post('/api/export/comprehensive-report')
      .send({
        sso_school_code: 'TEST_SCHOOL',
        month: '2024-03',
        include_school_summary: true,
        include_teacher_ranking: false,
        include_trend_analysis: false,
        include_completion_analysis: false,
      });

    expect(result.status).toBeGreaterThanOrEqual(200);
    
    if (result.status === 200) {
      expect(result.headers['content-type']).toContain('spreadsheet');
      expect(result.headers['content-disposition']).toContain('attachment');
    } else {
      expect(result.body).toHaveProperty('errCode');
      expect(result.body).toHaveProperty('msg');
    }
  });

  it('should POST /api/export/incomplete-students', async () => {
    const result = await createHttpRequest(app)
      .post('/api/export/incomplete-students')
      .send({
        sso_school_code: 'TEST_SCHOOL',
        month: '2024-03',
        include_contact_info: true,
      });

    expect(result.status).toBeGreaterThanOrEqual(200);
    
    if (result.status === 200) {
      expect(result.headers['content-type']).toContain('spreadsheet');
      expect(result.headers['content-disposition']).toContain('attachment');
    } else {
      expect(result.body).toHaveProperty('errCode');
      expect(result.body).toHaveProperty('msg');
    }
  });

  it('should POST /api/export/general', async () => {
    const result = await createHttpRequest(app)
      .post('/api/export/general')
      .send({
        export_type: 'school_statistics',
        sso_school_code: 'TEST_SCHOOL',
        month: '2024-03',
      });

    expect(result.status).toBeGreaterThanOrEqual(200);
    
    if (result.status === 200) {
      expect(result.headers['content-type']).toContain('spreadsheet');
      expect(result.headers['content-disposition']).toContain('attachment');
    } else {
      expect(result.body).toHaveProperty('errCode');
      expect(result.body).toHaveProperty('msg');
    }
  });

  it('should handle validation errors', async () => {
    const result = await createHttpRequest(app)
      .post('/api/export/school-statistics')
      .send({
        // 缺少必需的 sso_school_code 参数
        month: '2024-03',
      });

    expect(result.status).toBe(422); // 验证错误
    expect(result.body).toHaveProperty('message');
  });

  it('should handle invalid export type', async () => {
    const result = await createHttpRequest(app)
      .post('/api/export/general')
      .send({
        export_type: 'invalid_type',
        sso_school_code: 'TEST_SCHOOL',
        month: '2024-03',
      });

    expect(result.status).toBe(422); // 验证错误
    expect(result.body).toHaveProperty('message');
  });

  it('should handle invalid month format', async () => {
    const result = await createHttpRequest(app)
      .post('/api/export/school-statistics')
      .send({
        sso_school_code: 'TEST_SCHOOL',
        month: '2024-3', // 错误的月份格式
      });

    expect(result.status).toBe(422); // 验证错误
    expect(result.body).toHaveProperty('message');
  });
});
